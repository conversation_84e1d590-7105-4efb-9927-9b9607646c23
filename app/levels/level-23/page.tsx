/**
 * LEVEL-23 QUIZ IMPLEMENTATION - TECHDOS GAME
 *
 * OVERVIEW:
 * Level-23 represents the twenty-third stage of the TechDOS quiz game, featuring a comprehensive
 * jumbled sentence puzzle format designed to test language comprehension and unscrambling skills.
 * Players must identify the correct unscrambled version from multiple choice options.
 *
 * QUIZ MECHANICS:
 * - Format: Jumbled Sentence Puzzles with Multiple Choice Questions (MCQ) with 4 options each
 * - Total Questions: 4 diverse jumbled sentences covering literature, geography, science, and philosophy
 * - Hint System: Each question includes a helpful hint that can be revealed
 * - Timer Integration: Real-time countdown with global game timer
 * - Navigation Protection: Prevents accidental page refresh/navigation during quiz
 *
 * GAME FLOW:
 * 1. Question Selection: Questions are presented sequentially (no random order)
 * 2. Answer Selection: Players choose from 4 multiple-choice options (A, B, C, D)
 * 3. Hint Usage: Optional hints available for each question (affects scoring)
 * 4. Answer Submission: Submit selected answer or skip to next question
 * 5. Progress Tracking: Visual progress bar and question counter
 * 6. Level Completion: Automatic progression after all questions answered/skipped
 *
 * STATISTICS TRACKING:
 * - Correct Answers: Number of questions answered correctly
 * - Incorrect Answers: Number of questions answered incorrectly
 * - Skipped Questions: Number of questions skipped without answering
 * - Hints Used: Total number of hints revealed during the level
 * - Time Taken: Duration from level start to completion
 * - Consecutive Correct: Tracks streaks for bonus calculations
 *
 * SCORING ALGORITHM:
 * Base Points:
 * - Correct Answer (no hint): 1500 points
 * - Correct Answer (with hint): 1000 points
 * - Incorrect Answer: -400 points penalty
 * - Skipped Question: -750 points penalty
 *
 * Bonus Systems:
 * - Consecutive Correct Bonus: +200 points for every 3 consecutive correct answers
 * - Time Bonus (based on completion speed):
 *   * Under 1 min: +250 points
 *   * 1-1.5 min: +225 points
 *   * 1.5-2 min: +200 points
 *   * 2-2.5 min: +175 points
 *   * 2.5-3 min: +150 points
 *   * 3-3.5 min: +125 points
 *   * 3.5-4 min: +100 points
 *   * 4-4.5 min: +75 points
 *   * 4.5-5 min: +50 points
 *   * 5-5.5 min: +25 points
 *   * Over 5.5 min: No time bonus
 *
 * FINAL SCORE CALCULATION:
 * Total Score = (Base Points) + (Consecutive Bonus) + (Time Bonus)
 * Minimum Score: 0 (negative scores are clamped to zero)
 *
 * INTEGRATION FEATURES:
 * - Global Timer: Respects game-wide time limits and displays remaining time
 * - Team Management: Updates team statistics and progression status
 * - API Integration: Real-time updates to database for scores and statistics
 * - Navigation Control: Prevents data loss through page navigation protection
 * - Toast Notifications: User feedback for actions and errors
 *
 * LEVEL COMPLETION SUMMARY:
 * Upon completion, players receive detailed feedback including:
 * - Performance breakdown (correct/incorrect/skipped/hints)
 * - Time taken to complete the level
 * - Score breakdown with bonuses and penalties
 * - Performance rating based on accuracy and speed
 * - Navigation to next level (Level 24)
 */

"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { Trophy, Timer, HelpCircle, SkipForward, ArrowRight, CheckCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import { Team, getGameTimeRemaining, formatTimeRemaining, getGameTimerStatus } from "@/lib/supabase";

interface Question {
  id: number;
  question: string;
  options: string[];
  correct: string;
  hint: string;
}

/**
 * LEVEL-23 QUESTION BANK
 *
 * A collection of 4 jumbled sentence puzzles covering:
 * - Literature & Poetry (Edgar Allan Poe's "The Raven")
 * - Geography & World Knowledge (Sahara Desert facts)
 * - Science & Astronomy (Saturn and its moons)
 * - Philosophy & Human Nature (democracy and grammar)
 *
 * Each question includes:
 * - A jumbled sentence that needs to be unscrambled
 * - 4 carefully crafted options with plausible alternatives
 * - One correct unscrambled answer
 * - A helpful hint that provides context without giving away the answer
 */
const questions: Question[] = [
  {
    id: 1,
    question: "Unscramble this jumbled sentence: 'Edgar Alnla Poe worte 'Teh Rvaen' to scraes teh peolp at nhgit, wihle teh wolfs ulnig ot eht mnonr, csreahd eht slacne.'",
    options: [
      "Edgar Allan Poe wrote 'The Raven' to scare the people at night, while the wolves howling at the moon, crashed the silence.",
      "Edgar Allan Poe wrote 'The Raven' to please the people at night, while the wolves running to the moon, broke the silence.",
      "Edgar Allan Poe created 'The Raven' to frighten the people at night, while the wolves crying at the moon, shattered the silence.",
      "Edgar Allan Poe composed 'The Raven' to terrify the people at night, while the wolves singing to the moon, disturbed the silence."
    ],
    correct: "Edgar Allan Poe wrote 'The Raven' to scare the people at night, while the wolves howling at the moon, crashed the silence.",
    hint: "Focus on the famous American poet and his most well-known poem about a dark bird, and think about what wolves do at night."
  },
  {
    id: 2,
    question: "Unscramble this jumbled sentence: 'Evrneyoen knwos taht hte lrigseet dsert in hte wrold is eht ahcbl Dsesert, sppraed on 9.2 mln qkaumre.'",
    options: [
      "Everyone knows that the largest desert in the world is the Sahara Desert, spread on 9.2 million square km.",
      "Everyone knows that the biggest desert in the world is the Gobi Desert, spread on 9.2 million square km.",
      "Everyone knows that the greatest desert in the world is the Kalahari Desert, spread on 9.2 million square km.",
      "Everyone knows that the widest desert in the world is the Arabian Desert, spread on 9.2 million square km."
    ],
    correct: "Everyone knows that the largest desert in the world is the Sahara Desert, spread on 9.2 million square km.",
    hint: "Think about the most famous desert in Africa, known for its vast size and sandy dunes."
  },
  {
    id: 3,
    question: "Unscramble this jumbled sentence: 'A slyciendf, wlhiie a gnihtarc teh rceaat of ytinutar, dsevoeicrd teh rpidyc fo eht lciham.'",
    options: [
      "A scientist, while charting the crater of Saturn, discovered the orbit of the moons.",
      "A scientist, while studying the crater of Jupiter, discovered the orbit of the planets.",
      "A scientist, while mapping the crater of Mars, discovered the orbit of the asteroids.",
      "A scientist, while exploring the crater of Venus, discovered the orbit of the comets."
    ],
    correct: "A scientist, while charting the crater of Saturn, discovered the orbit of the moons.",
    hint: "Think about the ringed planet in our solar system and what celestial bodies orbit around it."
  },
  {
    id: 4,
    question: "Unscramble this jumbled sentence: 'A phsloiphre whti a lcookaed ppie, sniurttg na rwod, dsicovered eht tdyoemrca of humnas, whtiout tcuagh a sinlgel grmaer ruel.'",
    options: [
      "A philosopher with a crooked pipe, stirring a word, discovered the democracy of humans, without touching a single grammar rule.",
      "A philosopher with a broken pipe, mixing a word, discovered the freedom of humans, without learning a single grammar rule.",
      "A philosopher with a curved pipe, changing a word, discovered the equality of humans, without knowing a single grammar rule.",
      "A philosopher with a bent pipe, creating a word, discovered the rights of humans, without studying a single grammar rule."
    ],
    correct: "A philosopher with a crooked pipe, stirring a word, discovered the democracy of humans, without touching a single grammar rule.",
    hint: "Think about a deep thinker who might smoke a pipe and contemplate the nature of human governance and language."
  }
];

export default function Level23Page() {
  const [team, setTeam] = useState<Team | null>(null);
  const [initialTeamStats, setInitialTeamStats] = useState<{
    correct_questions: number;
    incorrect_questions: number;
    skipped_questions: number;
    hint_count: number;
  } | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<string>("");
  const [showHint, setShowHint] = useState(false);
  const [levelStartTime] = useState<Date>(new Date());
  const [timeRemaining, setTimeRemaining] = useState<number>(0);
  const [timerStatus, setTimerStatus] = useState<'not_started' | 'active' | 'expired'>('not_started');
  const [loading, setLoading] = useState(true);
  const [isCompleted, setIsCompleted] = useState(false);
  const [completionTimeMinutes, setCompletionTimeMinutes] = useState<number>(0);
  const [skipLoading, setSkipLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [levelStats, setLevelStats] = useState({
    correct: 0,
    incorrect: 0,
    skipped: 0,
    hintsUsed: 0
  });
  const router = useRouter();

  const fetchTeamData = useCallback(async (teamCode: string) => {
    try {
      const response = await fetch(`/api/teams/${teamCode}`);
      if (!response.ok) {
        throw new Error('Failed to fetch team data');
      }
      const teamData = await response.json();
      setTeam(teamData);

      // Store initial team statistics to track level-specific changes
      setInitialTeamStats({
        correct_questions: teamData.correct_questions,
        incorrect_questions: teamData.incorrect_questions,
        skipped_questions: teamData.skipped_questions,
        hint_count: teamData.hint_count
      });

      if (teamData.current_level > 23) {
        toast.info("You've already completed this level!");
        router.push('/levels');
        return;
      }

      if (teamData.current_level < 23) {
        toast.error("You need to complete previous levels first!");
        router.push('/levels');
        return;
      }

      setLoading(false);
    } catch (error) {
      console.error('Error fetching team data:', error);
      toast.error("Failed to load team data");
      router.push('/');
    }
  }, [router]);

  useEffect(() => {
    const teamCode = localStorage.getItem('team_code');
    if (!teamCode) {
      toast.error("No team found. Please register first.");
      router.push('/');
      return;
    }

    fetchTeamData(teamCode);

    // Prevent navigation during quiz
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      e.preventDefault();
      e.returnValue = '';
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [fetchTeamData, router]);

  useEffect(() => {
    if (team) {
      const timer = setInterval(() => {
        const remaining = getGameTimeRemaining(team);
        const status = getGameTimerStatus(team);

        setTimeRemaining(remaining);
        setTimerStatus(status);

        if (status === 'expired' && timerStatus !== 'expired') {
          toast.error("Time's up! The game has ended.");
        }
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [team, timerStatus]);

  const getTimerDisplay = (): { text: string; className: string } => {
    switch (timerStatus) {
      case 'not_started':
        return { text: 'Game Not Started', className: 'text-gray-500' };
      case 'expired':
        return { text: '00:00:00', className: 'text-red-600' };
      case 'active':
        return { text: formatTimeRemaining(timeRemaining), className: 'text-red-600' };
      default:
        return { text: 'Game Not Started', className: 'text-gray-500' };
    }
  };

  const updateTeamStats = async (stats: Record<string, number>) => {
    const teamCode = localStorage.getItem('team_code');
    if (!teamCode) return;

    try {
      await fetch(`/api/teams/${teamCode}/stats`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(stats)
      });
    } catch (error) {
      console.error('Error updating stats:', error);
    }
  };

  const handleHint = () => {
    if (!showHint) {
      setShowHint(true);
      const newStats = { ...levelStats };
      newStats.hintsUsed++;
      setLevelStats(newStats);
    }
  };

  const handleAnswer = async (answer: string) => {
    if (submitLoading || !answer) return;
    
    setSubmitLoading(true);
    
    try {
      const currentQuestion = questions[currentQuestionIndex];
      const isCorrect = answer === currentQuestion.correct;

      // Update local stats
      const newStats = { ...levelStats };
      if (isCorrect) {
        newStats.correct++;
      } else {
        newStats.incorrect++;
      }
      setLevelStats(newStats);

      // Update team stats in database
      if (!team) return;

      const updatedStats = {
        correct_questions: team.correct_questions + (isCorrect ? 1 : 0),
        incorrect_questions: team.incorrect_questions + (isCorrect ? 0 : 1),
        hint_count: team.hint_count + (showHint ? 1 : 0)
      };

      await updateTeamStats(updatedStats);

      // Move to next question or complete level
      if (currentQuestionIndex < questions.length - 1) {
        setCurrentQuestionIndex(currentQuestionIndex + 1);
        setSelectedAnswer("");
        setShowHint(false);
      } else {
        completeLevel();
      }
    } catch (error) {
      console.error('Error handling answer:', error);
      toast.error("Failed to submit answer. Please try again.");
    } finally {
      setSubmitLoading(false);
    }
  };

  const handleSkip = async () => {
    if (skipLoading) return;
    
    setSkipLoading(true);
    
    try {
      const newStats = { ...levelStats };
      newStats.skipped++;
      setLevelStats(newStats);

      if (!team) return;

      const updatedStats = {
        skipped_questions: team.skipped_questions + 1,
        hint_count: team.hint_count + (showHint ? 1 : 0)
      };

      await updateTeamStats(updatedStats);

      if (currentQuestionIndex < questions.length - 1) {
        setCurrentQuestionIndex(currentQuestionIndex + 1);
        setSelectedAnswer("");
        setShowHint(false);
      } else {
        completeLevel();
      }
    } catch (error) {
      console.error('Error skipping question:', error);
      toast.error("Failed to skip question. Please try again.");
    } finally {
      setSkipLoading(false);
    }
  };

  /**
   * ENHANCED SCORING ALGORITHM
   *
   * Calculates the final score for Level-23 based on multiple factors:
   *
   * BASE SCORING:
   * - Correct answers without hints: 1500 points each
   * - Correct answers with hints: 1000 points each (500 point penalty for hint usage)
   * - Incorrect answers: -400 points penalty each
   * - Skipped questions: -750 points penalty each
   *
   * BONUS SYSTEMS:
   * - Consecutive Correct Bonus: +200 points for every 3 consecutive correct answers
   * - Time Bonus: Rewards fast completion with decreasing bonuses based on time taken
   *
   * PERFORMANCE RATING:
   * - Excellent: 90%+ accuracy, under 3 minutes
   * - Good: 70%+ accuracy, under 4 minutes
   * - Average: 50%+ accuracy, under 5 minutes
   * - Needs Improvement: Below 50% accuracy or over 5 minutes
   */
  const calculateScore = (completionTime?: number): {
    totalScore: number;
    baseScore: number;
    timeBonus: number;
    consecutiveBonus: number;
    penalties: number;
    timeTaken: number;
    accuracy: number;
    performanceRating: string;
  } => {
    // Use provided completion time if available, otherwise calculate from current time
    const timeTaken = completionTime !== undefined ?
      completionTime :
      (new Date().getTime() - levelStartTime.getTime()) / 1000 / 60; // minutes
    const totalQuestions = levelStats.correct + levelStats.incorrect + levelStats.skipped;
    const accuracy = totalQuestions > 0 ? (levelStats.correct / totalQuestions) * 100 : 0;

    // Base scoring calculation
    const correctWithoutHints = Math.max(0, levelStats.correct - levelStats.hintsUsed);
    const correctWithHints = Math.min(levelStats.correct, levelStats.hintsUsed);

    let baseScore = 0;
    baseScore += correctWithoutHints * 1500; // Full points for unassisted correct answers
    baseScore += correctWithHints * 1000;    // Reduced points for hint-assisted answers

    // Penalties
    const penalties = (levelStats.incorrect * 400) + (levelStats.skipped * 750);

    // Consecutive correct bonus
    const consecutiveBonus = Math.floor(levelStats.correct / 3) * 200;

    // Time bonus calculation
    let timeBonus = 0;
    if (timeTaken < 1) timeBonus = 250;
    else if (timeTaken < 1.5) timeBonus = 225;
    else if (timeTaken < 2) timeBonus = 200;
    else if (timeTaken < 2.5) timeBonus = 175;
    else if (timeTaken < 3) timeBonus = 150;
    else if (timeTaken < 3.5) timeBonus = 125;
    else if (timeTaken < 4) timeBonus = 100;
    else if (timeTaken < 4.5) timeBonus = 75;
    else if (timeTaken < 5) timeBonus = 50;
    else if (timeTaken < 5.5) timeBonus = 25;

    // Performance rating
    let performanceRating = "Needs Improvement";
    if (accuracy >= 90 && timeTaken < 3) performanceRating = "Excellent";
    else if (accuracy >= 70 && timeTaken < 4) performanceRating = "Good";
    else if (accuracy >= 50 && timeTaken < 5) performanceRating = "Average";

    const totalScore = Math.max(0, baseScore + consecutiveBonus + timeBonus - penalties);

    return {
      totalScore,
      baseScore,
      timeBonus,
      consecutiveBonus,
      penalties,
      timeTaken,
      accuracy,
      performanceRating
    };
  };

  const completeLevel = async () => {
    if (!team) return;

    const teamCode = localStorage.getItem('team_code');
    if (!teamCode) return;

    // Calculate exact time taken to complete the level at this moment
    const timeTaken = (new Date().getTime() - levelStartTime.getTime()) / 1000 / 60; // minutes
    setCompletionTimeMinutes(timeTaken);

    const scoreData = calculateScore(timeTaken);
    const newTotalScore = team.score + scoreData.totalScore;
    const newLevel = 24;

    try {
      // toast.success(`Level 23 completed! Score: ${scoreData.totalScore.toLocaleString()} points`);

      // Update team data locally for immediate UI feedback
      setTeam({
        ...team,
        score: newTotalScore,
        current_level: newLevel
      });

      if (initialTeamStats) {
        // Calculate the final absolute values that should be in the database
        const finalStats = {
          correct_questions: initialTeamStats.correct_questions + levelStats.correct,
          incorrect_questions: initialTeamStats.incorrect_questions + levelStats.incorrect,
          skipped_questions: initialTeamStats.skipped_questions + levelStats.skipped,
          hint_count: initialTeamStats.hint_count + levelStats.hintsUsed
        };

        console.log('Level completion - Final stats update:', {
          initialStats: initialTeamStats,
          levelStats: levelStats,
          finalStats: finalStats
        });

        // Update final statistics to ensure database accuracy
        await fetch(`/api/teams/${teamCode}/stats`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(finalStats)
        });
      }

      // Update score and level
      await fetch(`/api/teams/${teamCode}/score`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          score: newTotalScore,
          current_level: newLevel
        })
      });

      setIsCompleted(true);
    } catch (error) {
      console.error('Error completing level:', error);
      toast.error("Failed to save progress. Please try again.");
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">Loading Level 23...</p>
        </div>
      </div>
    );
  }

  if (!team) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-lg text-gray-600">Failed to load team data.</p>
          <Button onClick={() => router.push('/')} className="mt-4">
            Return to Home
          </Button>
        </div>
      </div>
    );
  }

  if (isCompleted) {
    const scoreData = calculateScore(completionTimeMinutes);

    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50 flex items-center justify-center p-4">
        <Card className="max-w-4xl mx-auto">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <CardTitle className="text-3xl font-bold text-green-700">Level 23 Complete!</CardTitle>
            <div className="mt-2">
              <Badge variant="outline" className={`text-lg px-4 py-2 ${
                scoreData.performanceRating === 'Excellent' ? 'bg-green-50 text-green-700 border-green-200' :
                scoreData.performanceRating === 'Good' ? 'bg-blue-50 text-blue-700 border-blue-200' :
                scoreData.performanceRating === 'Average' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' :
                'bg-red-50 text-red-700 border-red-200'
              }`}>
                {scoreData.performanceRating}
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Score Summary */}
            <div className="text-center">
              <div className="text-4xl font-bold text-purple-600 mb-2">
                +{scoreData.totalScore.toLocaleString()} Points
              </div>
              <p className="text-gray-600">Added to your team score</p>
            </div>

            {/* Performance Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{levelStats.correct}</div>
                <div className="text-sm text-green-700">Correct</div>
              </div>
              <div className="text-center p-4 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">{levelStats.incorrect}</div>
                <div className="text-sm text-red-700">Incorrect</div>
              </div>
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">{levelStats.skipped}</div>
                <div className="text-sm text-yellow-700">Skipped</div>
              </div>
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{levelStats.hintsUsed}</div>
                <div className="text-sm text-blue-700">Hints Used</div>
              </div>
            </div>

            {/* Score Breakdown */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-semibold text-gray-800 mb-3">Score Breakdown</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Base Score:</span>
                  <span className="font-medium">+{scoreData.baseScore.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Time Bonus:</span>
                  <span className="font-medium text-green-600">+{scoreData.timeBonus}</span>
                </div>
                <div className="flex justify-between">
                  <span>Consecutive Bonus:</span>
                  <span className="font-medium text-blue-600">+{scoreData.consecutiveBonus}</span>
                </div>
                <div className="flex justify-between">
                  <span>Penalties:</span>
                  <span className="font-medium text-red-600">-{scoreData.penalties}</span>
                </div>
                <hr className="my-2" />
                <div className="flex justify-between font-bold">
                  <span>Total Score:</span>
                  <span className="text-purple-600">+{scoreData.totalScore.toLocaleString()}</span>
                </div>
              </div>
            </div>

            {/* Additional Stats */}
            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <div className="text-lg font-semibold text-gray-800">
                  {scoreData.accuracy.toFixed(1)}%
                </div>
                <div className="text-sm text-gray-600">Accuracy</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-gray-800">
                  {scoreData.timeTaken.toFixed(1)}m
                </div>
                <div className="text-sm text-gray-600">Time Taken</div>
              </div>
            </div>

            <Button
              onClick={() => router.push('/levels')}
              className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-lg py-3"
            >
              Continue to Level 24
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const currentQuestion = questions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / questions.length) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50">
      {/* Header */}
      <header className="sticky top-0 z-50 bg-white/80 backdrop-blur-sm border-b border-purple-200">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                Level 23
              </Badge>
              <span className="text-lg font-semibold text-gray-800">{team.team_name}</span>
            </div>

            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Trophy className="h-5 w-5 text-yellow-600" />
                <span className="text-lg font-semibold text-gray-800">
                  {team.score.toLocaleString()} pts
                </span>
              </div>

              <div className="flex items-center space-x-2">
                <Timer className={`h-5 w-5 ${timerStatus === 'not_started' ? 'text-gray-500' : 'text-red-600'}`} />
                <span className={`text-lg font-mono font-semibold ${getTimerDisplay().className}`}>
                  {getTimerDisplay().text}
                </span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto w-full">
          {/* Progress */}
          <div className="mb-8">
            <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
              <span>Question {currentQuestionIndex + 1} of {questions.length}</span>
              <span>{Math.round(progress)}% Complete</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          {/* Question Card */}
          <Card className="mb-8 w-full overflow-hidden">
            <CardHeader className="px-6 md:px-8">
              <CardTitle className="text-xl md:text-2xl text-center text-gray-800 break-words">
                {currentQuestion.question}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 px-6 md:px-8">
              {/* Options */}
              <div className="grid gap-3">
                {currentQuestion.options.map((option, index) => (
                  <Button
                    key={index}
                    variant={selectedAnswer === option ? "default" : "outline"}
                    className={`p-4 h-auto text-left justify-start break-words ${
                      selectedAnswer === option
                        ? "bg-purple-600 hover:bg-purple-700"
                        : "hover:bg-purple-50"
                    }`}
                    onClick={() => setSelectedAnswer(option)}
                  >
                    <span className="font-medium mr-3">{String.fromCharCode(65 + index)}.</span>
                    <span className="flex-1">{option}</span>
                  </Button>
                ))}
              </div>

              {/* Hint */}
              {showHint && (
                <Alert className="bg-blue-50 border-blue-200">
                  <HelpCircle className="h-4 w-4 text-blue-600" />
                  <AlertDescription className="text-blue-700">
                    <strong>Hint:</strong> {currentQuestion.hint}
                  </AlertDescription>
                </Alert>
              )}

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4">
                <Button
                  variant="outline"
                  onClick={handleHint}
                  disabled={showHint}
                  className="flex-1"
                >
                  <HelpCircle className="mr-2 h-4 w-4" />
                  {showHint ? "Hint Shown" : "Show Hint"}
                </Button>

                <Button
                  variant="outline"
                  onClick={handleSkip}
                  disabled={skipLoading}
                  className="flex-1 text-yellow-600 border-yellow-200 hover:bg-yellow-50"
                >
                  {skipLoading ? (
                    <>
                      <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-yellow-600 border-t-transparent" />
                      Skipping...
                    </>
                  ) : (
                    <>
                      <SkipForward className="mr-2 h-4 w-4" />
                      Skip Question
                    </>
                  )}
                </Button>

                <Button
                  onClick={() => handleAnswer(selectedAnswer)}
                  disabled={!selectedAnswer || submitLoading}
                  className="flex-1 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                >
                  {submitLoading ? (
                    <>
                      <div className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-white border-t-transparent" />
                      Submitting...
                    </>
                  ) : (
                    <>
                      Submit Answer
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
